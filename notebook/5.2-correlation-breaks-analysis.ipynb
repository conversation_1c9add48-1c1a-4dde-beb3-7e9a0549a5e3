import os
import numpy as np
import pandas as pd
import yaml
from datetime import timedelta, datetime
import matplotlib.dates as mdates
import matplotlib.pyplot as plt
from collections import defaultdict

from itertools import combinations
%matplotlib inline

%run functions

df = pd.read_parquet("../data/2022-2023.parquet")
events_df = pd.read_parquet("../data/labels.parquet")

display_scrollable_table(events_df)

with open("../data/event_feature.yaml", "r") as file:
    data = yaml.safe_load(file)

# Extract feature columns in order, using the first event as reference
for event_info in data.values():
    feature_columns = list(event_info.get("features", {}).keys())
    break

# Binary matrix
matrix_data = []
for event_name, event_info in data.items():
    active_tags = [tag for tag, is_active in event_info.get("features", {}).items() if is_active]
    datetime = event_info.get("datetime")

    row = [event_name, datetime] + [1 if tag in active_tags else 0 for tag in feature_columns]
    matrix_data.append(row)

columns = ["event_id", "datetime"] + feature_columns

event_features = pd.DataFrame(matrix_data, columns=columns)
event_features.set_index("event_id", drop=False, inplace=True)

display_scrollable_table(event_features)


event_features.index

for event_name, row in event_features.iterrows():
    count = row.drop(["event_id", "datetime"]).sum()
    print(f"{event_name}: {int(count)} features")


feature_counts = event_features.drop(columns=["event_id", "datetime"]).sum()

for feature, count in feature_counts.items():
    print(f"{feature}: {int(count)}")


window_size = 300 # 5 hours
step_size = 10

def generate_event_feature_pairs(hours_before=12):
    event_inputs = []

    shutdown_events = events_df[events_df["status_change"] == "shutdown"]

    for _, event_row in shutdown_events.iterrows():        
        # Ensure both datetime fields are pandas datetime, rounded to the same second
        events_df["datetime"] = pd.to_datetime(events_df["datetime"]).dt.round("s")
        event_features["datetime"] = pd.to_datetime(event_features["datetime"]).dt.round("s")

        event_time = event_row["datetime"]

        # Match event_features row by datetime
        matched_rows = event_features[event_features["datetime"] == event_time]
        if matched_rows.empty:
            print(f"No event_features match for shutdown at {event_time}")
            continue

        event_id = matched_rows.index[0]
        active_features = matched_rows.drop(columns=["datetime"]).iloc[0]
        active_cols = active_features[active_features == 1].index.tolist()

        start_time = event_time - timedelta(hours=hours_before)
        end_time = event_time

        window_df = df[(df["datetime"] >= start_time) & (df["datetime"] <= end_time)].copy()
        window_df = window_df.drop_duplicates(subset="datetime").set_index("datetime")

        feature_pairs = list(combinations(active_cols, 2))

        print(f"Shutdown at {event_time}, {len(feature_pairs)} feature pairs")

        event_inputs.append({
            "event_id": event_id,
            "event_time": event_time,
            "window_df": window_df,
            "feature_pairs": feature_pairs
        })

    return event_inputs


feature_pairs_per_event = generate_event_feature_pairs()

def detect_corr_pair_anomalies(
    feature_pairs_per_event,
    pct_change_threshold=0.10,
    max_gap_minutes=60,
):
    all_anomalies = []

    for event in feature_pairs_per_event:
        event_id = event["event_id"]
        event_time = event["event_time"]
        window_df = event["window_df"]
        feature_pairs = event["feature_pairs"]
        active_cols = list(set([col for pair in feature_pairs for col in pair]))

        # Compute rolling correlation matrix once for all feature pairs
        rolling_corr_matrix = (
            window_df[active_cols] # select active features only
            .rolling(window=window_size, min_periods=int(window_size * 0.6)) 
            .corr() # returns index: MultiIndex(timestamp, feature_1), columns: feature_2
            .dropna() 
            .unstack(level=1) # reshape: move feature_1 from index to columns
        )  # returns index: timestamp, columns: MultiIndex(feature_1, feature_2)
                
        event_anomalies = {}

        for x_col, y_col in feature_pairs:
            try:
                if (x_col, y_col) in rolling_corr_matrix.columns:
                    corr_series = rolling_corr_matrix[(x_col, y_col)]
                elif (y_col, x_col) in rolling_corr_matrix.columns:
                    corr_series = rolling_corr_matrix[(y_col, x_col)]
                else:
                    continue

                corr_series = corr_series[::step_size]

                pct_change = corr_series.pct_change(periods=2, fill_method=None).abs()
                anomaly_mask = pct_change > pct_change_threshold
                anomaly_periods = pct_change[anomaly_mask].index

                # Group into time ranges
                ranges = []
                if len(anomaly_periods) > 0:
                    sorted_times = sorted(anomaly_periods)
                    current_range_start = current_range_end = sorted_times[0]

                    for t in sorted_times[1:]:
                        gap = (t - current_range_end).total_seconds() / 60
                        if gap <= max_gap_minutes:
                            current_range_end = t
                        else:
                            ranges.append((current_range_start, current_range_end))
                            current_range_start = current_range_end = t
                    ranges.append((current_range_start, current_range_end))

                event_anomalies[f"{x_col} - {y_col}"] = {
                    "timestamp": anomaly_periods,
                    "correlation": corr_series[anomaly_periods],
                    "pct_change": pct_change[anomaly_mask],
                    "ranges": ranges,
                    "full_correlation": corr_series,
                    "full_pct_change": pct_change,
                }
            except KeyError as e:
                # Skip if pair not in computed matrix
                continue

        all_anomalies.append({
            "event_id": event_id,
            "event_time": event_time,
            "anomalies": event_anomalies
        })

    return all_anomalies


anomalies_per_event = detect_corr_pair_anomalies(
    feature_pairs_per_event,
    pct_change_threshold=0.10,
    max_gap_minutes=60
)


print(anomalies_per_event[7])

def generate_stats_df(anomalies_per_event):
    pair_stats = defaultdict(lambda: {
        "event_ids": set(),
        "total_anomalies": 0,
        "total_duration_mins": 0.0,
        "all_durations": [],
        "time_to_shutdown_mins": [],
        "max_pct_change": 0.0,
    })

    for event in anomalies_per_event:
        event_id = event["event_id"]
        event_time = event["event_time"]
        anomalies = event["anomalies"]

        for pair_name, data in anomalies.items():
            if len(data["timestamp"]) > 0:

                stats = pair_stats[pair_name]
                stats["event_ids"].add(event_id)

                # Count anomalies
                stats["total_anomalies"] += len(data["timestamp"])

                # Duration
                for start, end in data["ranges"]:
                    duration_mins = (end - start).total_seconds() / 60
                    stats["total_duration_mins"] += duration_mins
                    stats["all_durations"].append(duration_mins)

                    # How close is the anomaly to the shutdown time?
                    time_to_shutdown = (event_time - end).total_seconds() / 60
                    stats["time_to_shutdown_mins"].append(time_to_shutdown)

                # Max % change
                valid_pct_change = data["pct_change"][np.isfinite(data["pct_change"])] # Filter out inf and NaN
                max_change = valid_pct_change.max() if not valid_pct_change.empty else 0.0
                stats["max_pct_change"] = max(stats["max_pct_change"], max_change)

    rows = []
    for pair, stats in pair_stats.items():
        if not stats["event_ids"]:
            continue
        
        rows.append({
            "pair": pair,
            "event_count": len(stats["event_ids"]),
            "total_anomalies": stats["total_anomalies"],
            "total_duration_mins": stats["total_duration_mins"],
            "avg_duration": (
                sum(stats["all_durations"]) / len(stats["all_durations"])
                if stats["all_durations"] else 0.0
            ),
            "avg_time_to_shutdown": (
                sum(stats["time_to_shutdown_mins"]) / len(stats["time_to_shutdown_mins"])
                if stats["time_to_shutdown_mins"] else None
            ),
            "max_pct_change": stats["max_pct_change"]
        })

    stats_df = pd.DataFrame(rows).sort_values(by="event_count", ascending=False).reset_index(drop=True)
    return stats_df

stats_df = generate_stats_df(anomalies_per_event)

display_scrollable_table(stats_df)

def plot_event_anomalies(event_id, anomalies_per_event, df, hours_before=12):
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from datetime import timedelta

    # Match event
    matching = next((e for e in anomalies_per_event if e["event_id"] == event_id), None)
    if matching is None or not matching["anomalies"]:
        print(f"[Info] No data for event '{event_id}'")
        return []

    anomalies = matching["anomalies"]
    event_time = matching["event_time"]

    # Slice once
    start_time = event_time - timedelta(hours=hours_before)
    end_time = event_time
    window_df = df[(df["datetime"] >= start_time) & (df["datetime"] <= end_time)]
    if window_df.empty:
        print(f"[Warning] Empty window for event {event_id}")
        return []

    window_df = window_df.set_index("datetime", drop=True)

    figures = []

    # Loop through each correlation pair
    for pair_name, data in anomalies.items():
        # Create figure with two subplots side by side
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 4), gridspec_kw={'width_ratios': [1, 1]})
        
        # Get the full correlation series time range
        corr_series = data["full_correlation"]
        
        # Left subplot: Correlation plot
        ax1.plot(corr_series.index, corr_series.values, color='blue', alpha=0.6)

        # Anomaly markers
        if not data["timestamp"].empty:
            ax1.scatter(data["timestamp"], data["correlation"], color='red', s=15, zorder=5)

            # Highlight anomaly regions on both subplots
            for start, end in data["ranges"]:
                ax1.axvspan(start, end, color='red', alpha=0.15)
                
                duration_mins = (end - start).total_seconds() / 60
                ax1.text(start, -0.95, f"{start.strftime('%H:%M')} ({duration_mins:.0f}m)",
                        fontsize=7, rotation=45, color='black', va='bottom')

        # Add shutdown line
        ax1.axvline(event_time, color='red', linestyle='--', lw=1.2)
        
        # Format x-axis to show only hours
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # Set labels
        ax1.set_ylabel(f"{pair_name}\nCorr", rotation=0, labelpad=15, fontsize=9)
        ax1.set_ylim(-1.0, 1.0)
        ax1.grid(True, linestyle='--', alpha=0.3)
        ax1.set_xlabel("Time (HH:MM)", fontsize=10)
        ax1.set_title("Correlation", fontsize=11)
        
        # Right subplot: Feature values over time with dual y-axes
        # Extract the two feature names from the pair_name
        features = pair_name.split(" - ")
        if len(features) == 2:
            feature1, feature2 = features
            
            # Plot both features with dual y-axes
            if feature1 in window_df.columns and feature2 in window_df.columns:
                # Get the actual time range of the correlation data
                corr_start = corr_series.index.min() if not corr_series.empty else start_time
                corr_end = corr_series.index.max() if not corr_series.empty else end_time
                
                # Filter feature data to match correlation time range
                feature_df = window_df.loc[corr_start:corr_end]
                
                # Plot first feature on left y-axis
                ax2.plot(feature_df.index, feature_df[feature1], label=feature1, color='green', alpha=0.7)
                ax2.set_ylabel(feature1, color='green', fontsize=9)
                ax2.tick_params(axis='y', labelcolor='green')
                
                # Create twin axis for second feature
                ax2_twin = ax2.twinx()
                ax2_twin.plot(feature_df.index, feature_df[feature2], label=feature2, color='purple', alpha=0.7)
                ax2_twin.set_ylabel(feature2, color='purple', fontsize=9)
                ax2_twin.tick_params(axis='y', labelcolor='purple')
                
                # Highlight the same anomaly regions
                if not data["timestamp"].empty:
                    for start, end in data["ranges"]:
                        ax2.axvspan(start, end, color='red', alpha=0.15)
                
                # Add shutdown line
                ax2.axvline(event_time, color='red', linestyle='--', lw=1.2)
                
                # Format x-axis to show only hours
                ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                
                # Set labels
                ax2.set_xlabel("Time (HH:MM)", fontsize=10)
                ax2.grid(True, linestyle='--', alpha=0.3)
                
                # Create a custom legend for both y-axes
                lines1, labels1 = ax2.get_legend_handles_labels()
                lines2, labels2 = ax2_twin.get_legend_handles_labels()
                ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper right', fontsize=8)
                
                ax2.set_title("Feature Values", fontsize=11)
            else:
                ax2.text(0.5, 0.5, f"Features not found in data:\n{feature1}\n{feature2}", 
                         ha='center', va='center', transform=ax2.transAxes)
        else:
            ax2.text(0.5, 0.5, f"Could not parse feature names from:\n{pair_name}", 
                     ha='center', va='center', transform=ax2.transAxes)

        # Ensure both subplots have the same x-axis range based on correlation data
        if not corr_series.empty:
            corr_start = corr_series.index.min()
            corr_end = corr_series.index.max()
            ax1.set_xlim(corr_start, corr_end)
            ax2.set_xlim(corr_start, corr_end)
        else:
            ax1.set_xlim(start_time, end_time)
            ax2.set_xlim(start_time, end_time)

        fig.suptitle(
            f"{pair_name} correlation before shutdown at {event_time.strftime('%Y-%m-%d %H:%M:%S')}",
            fontsize=11
        )

        # Adjust layout
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        figures.append(fig)
        plt.close(fig)

    return figures

event_id = "event_0" 
figures = plot_event_anomalies(event_id, anomalies_per_event, df)

save_figures_to_pdf(figures, filename=f"../shutdown_correlation_plots/corr_anomalies_{event_id}.pdf")


# Generate and display percentage change plots
pct_change_figures = []
for events in anomalies_per_event:
    event_time = events["event_time"]
    anomalies = events["anomalies"]
    
    fig, axes = plt.subplots(
        nrows=len(anomalies), 
        ncols=2,
        figsize=(18, 3 * len(anomalies)), 
        sharey=False,
        sharex=True
    )

    for i, (pair_name, data) in enumerate(anomalies.items()):
        # Get correlation data
        corr_series = data["full_correlation"]
        pct_change = data["full_pct_change"]
        
        # Left subplot: Correlation
        ax_corr = axes[i][0] if len(anomalies) > 1 else axes[0]
        ax_corr.plot(corr_series.index, corr_series, color='blue', alpha=0.8)
        
        # Highlight anomaly ranges
        for start, end in data["ranges"]:
            ax_corr.axvspan(start, end, color='red', alpha=0.2)
        
        # Add shutdown marker
        ax_corr.axvline(event_time, color='red', linestyle='--')
        
        # Set labels and formatting
        ax_corr.set_ylabel(f"{pair_name}\nCorr", rotation=0, labelpad=20)
        ax_corr.set_ylim(-1.0, 1.0)
        ax_corr.grid(True)
        ax_corr.set_title("Correlation")
        
        # Right subplot: Percentage Change
        ax_pct = axes[i][1] if len(anomalies) > 1 else axes[1]
        ax_pct.plot(pct_change.index, pct_change, color='green', alpha=0.8)
        
        # Add threshold line
        ax_pct.axhline(0.15, color='red', linestyle='--', alpha=0.7, 
                      label='Threshold (0.15)')
        
        # Highlight anomaly points
        if len(data["timestamp"]) > 0:
            ax_pct.scatter(
                data["timestamp"], 
                pct_change[data["timestamp"]], 
                color='red', s=30, zorder=5
            )
        
        # Add shutdown marker
        ax_pct.axvline(event_time, color='red', linestyle='--')
        
        # Set labels and formatting
        ax_pct.set_ylabel("Pct Change", rotation=90)
        
        # Set y-limit to show detail but not extreme outliers
        y_max = min(pct_change.quantile(0.95) * 1.5, 1.0)
        ax_pct.set_ylim(0, y_max)
        
        ax_pct.grid(True)
        ax_pct.set_title("Percentage Change in Correlation")
        
        # Add legend to the first plot only
        if i == 0:
            ax_pct.legend()
    
    # Set common x-axis label and title
    if len(anomalies) > 1:
        axes[-1][0].set_xlabel("Time")
        axes[-1][1].set_xlabel("Time")
    else:
        axes[0].set_xlabel("Time")
        axes[1].set_xlabel("Time")
    
    plt.suptitle(
        f"Correlation Stability Analysis Before Shutdown ({event_time.strftime('%Y-%m-%d %H:%M:%S')})"
    )
    plt.tight_layout()
    pct_change_figures.append(fig)
    
display_scrollable_figures(pct_change_figures)

# Print summary of anomalies
for event in anomalies_per_event:
    event_time = event["event_time"]
    anomalies = event["anomalies"]
    
    print(f"\nShutdown event at {event_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    for pair_name, data in anomalies.items():
        ranges = data["ranges"]
        if ranges:
            # Calculate total duration
            total_duration = sum((end - start).total_seconds() / 60 for start, end in ranges)
            
            print(f"  {pair_name}: {len(ranges)} ranges, {total_duration:.1f} total minutes")
            
            # Print details for significant ranges (> 5 minutes)
            significant_ranges = [(start, end) for start, end in ranges 
                                 if (end - start).total_seconds() / 60 > 5]
            
            if significant_ranges:
                print("    Significant ranges:")
                for i, (start, end) in enumerate(significant_ranges):
                    duration_mins = (end - start).total_seconds() / 60
                    time_before = (event_time - end).total_seconds() / 60
                    print(f"      {start.strftime('%H:%M:%S')} to {end.strftime('%H:%M:%S')} "
                          f"({duration_mins:.1f} mins, {time_before:.1f} mins before shutdown)")
        else:
            print(f"  {pair_name}: No anomaly ranges detected")
