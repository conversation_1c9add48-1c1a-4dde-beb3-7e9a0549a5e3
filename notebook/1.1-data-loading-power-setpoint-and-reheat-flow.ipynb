{"cells": [{"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import polars as pl"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["df_raw = pl.read_csv(\"../data/Hot Reheat Flow and Power Setpoint_2022_2024_PI(Sheet2).csv\", skip_rows=7, infer_schema=False, has_header=False)\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["['column_1',\n", " 'column_2',\n", " 'column_3',\n", " 'column_4',\n", " 'column_5',\n", " 'column_6',\n", " 'column_7']"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["df_raw.columns"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["df_2022 = df_raw.select([\n", "    pl.col(\"column_1\").alias(\"datetime\"),\n", "    pl.col(\"column_2\").alias(\"Power_Setpoint\"),\n", "    pl.col(\"column_3\").alias(\"Reheat_Steam_Flow\"),\n", "])"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (5, 3)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>datetime</th><th>Power_Setpoint</th><th>Reheat_Steam_Flow</th></tr><tr><td>str</td><td>str</td><td>str</td></tr></thead><tbody><tr><td>&quot;01-Jan-22 00:00:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr><tr><td>&quot;01-Jan-22 00:01:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr><tr><td>&quot;01-Jan-22 00:02:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr><tr><td>&quot;01-Jan-22 00:03:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr><tr><td>&quot;01-Jan-22 00:04:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr></tbody></table></div>"], "text/plain": ["shape: (5, 3)\n", "┌────────────────────┬────────────────┬───────────────────┐\n", "│ datetime           ┆ Power_Setpoint ┆ Reheat_Steam_Flow │\n", "│ ---                ┆ ---            ┆ ---               │\n", "│ str                ┆ str            ┆ str               │\n", "╞════════════════════╪════════════════╪═══════════════════╡\n", "│ 01-Jan-22 00:00:00 ┆ 900            ┆ 661.1347656       │\n", "│ 01-Jan-22 00:01:00 ┆ 900            ┆ 661.1347656       │\n", "│ 01-Jan-22 00:02:00 ┆ 900            ┆ 661.1347656       │\n", "│ 01-Jan-22 00:03:00 ┆ 900            ┆ 661.1347656       │\n", "│ 01-Jan-22 00:04:00 ┆ 900            ┆ 661.1347656       │\n", "└────────────────────┴────────────────┴───────────────────┘"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df_2022.head()"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["df_2023 = df_raw.select([\n", "    pl.col(\"column_5\").alias(\"datetime\"),\n", "    pl.col(\"column_6\").alias(\"Power_Setpoint\"),\n", "    pl.col(\"column_7\").alias(\"Reheat_Steam_Flow\"),\n", "])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert timestamp"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["df_combined = pl.concat([df_2022, df_2023])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (5, 3)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>datetime</th><th>Power_Setpoint</th><th>Reheat_Steam_Flow</th></tr><tr><td>str</td><td>str</td><td>str</td></tr></thead><tbody><tr><td>&quot;01-Jan-22 00:00:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr><tr><td>&quot;01-Jan-22 00:01:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr><tr><td>&quot;01-Jan-22 00:02:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr><tr><td>&quot;01-Jan-22 00:03:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr><tr><td>&quot;01-Jan-22 00:04:00&quot;</td><td>&quot;900&quot;</td><td>&quot;661.1347656&quot;</td></tr></tbody></table></div>"], "text/plain": ["shape: (5, 3)\n", "┌────────────────────┬────────────────┬───────────────────┐\n", "│ datetime           ┆ Power_Setpoint ┆ Reheat_Steam_Flow │\n", "│ ---                ┆ ---            ┆ ---               │\n", "│ str                ┆ str            ┆ str               │\n", "╞════════════════════╪════════════════╪═══════════════════╡\n", "│ 01-Jan-22 00:00:00 ┆ 900            ┆ 661.1347656       │\n", "│ 01-Jan-22 00:01:00 ┆ 900            ┆ 661.1347656       │\n", "│ 01-Jan-22 00:02:00 ┆ 900            ┆ 661.1347656       │\n", "│ 01-Jan-22 00:03:00 ┆ 900            ┆ 661.1347656       │\n", "│ 01-Jan-22 00:04:00 ┆ 900            ┆ 661.1347656       │\n", "└────────────────────┴────────────────┴───────────────────┘"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined.head()  "]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["df = df_combined.with_columns([\n", "    pl.col(\"datetime\").cast(pl.Utf8).str.strip_chars().str.strptime(pl.Datetime, \"%d-%b-%y %H:%M:%S\", strict=False)\n", "    ])"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["df = df.with_columns(\n", "    pl.all().exclude(\"datetime\").cast(pl.Float64, strict=False)\n", ")\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (5, 3)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>datetime</th><th>Power_Setpoint</th><th>Reheat_Steam_Flow</th></tr><tr><td>datetime[μs]</td><td>f64</td><td>f64</td></tr></thead><tbody><tr><td>2022-01-01 00:00:00</td><td>900.0</td><td>661.134766</td></tr><tr><td>2022-01-01 00:01:00</td><td>900.0</td><td>661.134766</td></tr><tr><td>2022-01-01 00:02:00</td><td>900.0</td><td>661.134766</td></tr><tr><td>2022-01-01 00:03:00</td><td>900.0</td><td>661.134766</td></tr><tr><td>2022-01-01 00:04:00</td><td>900.0</td><td>661.134766</td></tr></tbody></table></div>"], "text/plain": ["shape: (5, 3)\n", "┌─────────────────────┬────────────────┬───────────────────┐\n", "│ datetime            ┆ Power_Setpoint ┆ Reheat_Steam_Flow │\n", "│ ---                 ┆ ---            ┆ ---               │\n", "│ datetime[μs]        ┆ f64            ┆ f64               │\n", "╞═════════════════════╪════════════════╪═══════════════════╡\n", "│ 2022-01-01 00:00:00 ┆ 900.0          ┆ 661.134766        │\n", "│ 2022-01-01 00:01:00 ┆ 900.0          ┆ 661.134766        │\n", "│ 2022-01-01 00:02:00 ┆ 900.0          ┆ 661.134766        │\n", "│ 2022-01-01 00:03:00 ┆ 900.0          ┆ 661.134766        │\n", "│ 2022-01-01 00:04:00 ┆ 900.0          ┆ 661.134766        │\n", "└─────────────────────┴────────────────┴───────────────────┘"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["df.write_parquet(\"../data/power_setpoint_reheat_flow.parquet\")"]}], "metadata": {"kernelspec": {"display_name": "turbine", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}