{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data loading and preprocessing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The goal of this notebook is to load and clean raw data into processed data, so that we only need to deal with raw data here, and only use processed data beyond this notebook."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import polars as pl\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load csv file, convert time column to datetime type"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["df = pl.read_csv(\"../data/2022-2023.csv\", infer_schema=False, low_memory=False)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'29-Jul-22 04:17:00'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Date & time stamp [DD:MMM:YYYY   HH:MM:SS]\"][300_500]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As seen in the date format of this dataset, the timestamp is a bit problematic to parse manually, especially the hour field that is occasionally two digits. Thankfully, built-in parsing takes this in stride just fine."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.with_columns(\n", "    pl.col(\"Date & time stamp [DD:MMM:YYYY   HH:MM:SS]\").str.strptime(pl.Datetime)\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert all columns except datetime to float type."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.sort(\"Date & time stamp [DD:MMM:YYYY   HH:MM:SS]\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Casting to float automatically stores the nulls as NaN\n", "# These NaNs will be stored correctly when output as parquet\n", "date_col = \"Date & time stamp [DD:MMM:YYYY   HH:MM:SS]\"\n", "df = df.with_columns(\n", "    pl.col(date_col), pl.all().exclude(date_col).cast(pl.Float64, strict=False)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (5, 76)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>Date &amp; time stamp [DD:MMM:YYYY   HH:MM:SS]</th><th>Shaft speed (RPM)</th><th>Power output (MW)</th><th>Live Steam Temperature A (°C)</th><th>Live Steam Temperature B (°C)</th><th>Delta Temp (A-B)</th><th>Delta Temp (B-A)</th><th>Live Steam Pressure A (bar)</th><th>Live Steam Pressure B (bar)</th><th>T HP Exhaust</th><th>Reheat Steam Temperature A (°C)</th><th>Reheat Steam Temperature B(Celcius)</th><th></th><th>Reheat Steam Pressure A (bar)</th><th>Reheat Steam Pressure B (bar)</th><th>Main Steam Stop Valve A</th><th>Main Steam Stop Valve B</th><th>Main Steam Control Valve A</th><th>Main Steam Control Valve B</th><th>IPCV A Valve Position (%)</th><th>IPCV B Valve Position (%)</th><th>IPSV A Valve Position (%)</th><th>IPSV B Valve Position (%)</th><th>HP Average Rotor Temperature (°C)</th><th>IP Average Rotor Temperature (°C)</th><th>P HP Stage 1</th><th>P IP Stage 1</th><th>1X Vib Shaft </th><th>1Y Vib Shaft</th><th>Vib Ped HPT 1</th><th>Vib Ped HPT 2</th><th>2X Vib Shaft</th><th>2Y Vib Shaft</th><th>2H Vib Ped ABS Thrust 1</th><th>2V Vib Ped ABS Thrust 2</th><th>3X Vib Shaft</th><th>3Y Vib Shaft</th><th>&hellip;</th><th>4X Vib Shaft</th><th>4Y Vib Shaft</th><th>4H Vib Ped LPT 1</th><th>4V Vib Ped LPT 2</th><th>5X Vib Shaft</th><th>5Y Vib Shaft</th><th>5H Vib Ped Gen DE 1</th><th>5V Vib Ped Gen DE 2</th><th>6X Vib Shaft</th><th>6Y Vib Shaft</th><th>6H Vib Ped Gen NDE 1</th><th>6V Vib Ped Gen NDE 2</th><th>7X Vib Shaft</th><th>7Y Vib Shaft</th><th>7H Vib Ped EXC 1</th><th>7V Vib Ped ECX 2</th><th>HP Differential Expansion Readings (mm)</th><th>IP Differential Expansion Readings</th><th>LP Differential Expansion Readings</th><th>Condenser pressure (mbar)</th><th>HP Steam Flow (kg/s)</th><th>IP Steam Flow </th><th>HP Exhaust Pressure (bar)</th><th>IP Exhaust Pressure A (bar)</th><th>IP Exhaust Pressure B (bar)</th><th>dP HP Strainer A</th><th>dP HP Strainer B</th><th>P MSV(A) Casing</th><th>P MSV(B) Casing</th><th>HP_ABS_Exp</th><th>IP_ABS_Exp</th><th>Thrust Position</th><th>HP_Eccentricity</th><th>HP_Rel Stress</th><th>HP Surface Stress</th><th>IP_Rel Stress</th><th>IP Surface Stress</th></tr><tr><td>datetime[μs]</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>&hellip;</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td><td>str</td></tr></thead><tbody><tr><td>null</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_BAA5…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBA1…</td><td>null</td><td>null</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBB1…</td><td>null</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAY2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAY2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD3…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD3…</td><td>&hellip;</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD4…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD4…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD4…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD4…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD5…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD5…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD5…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MKD5…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAB1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAC2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAG2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LAE1…</td><td>null</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_LBC1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAC0…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAC0…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAY2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAY2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAA1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAD1…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAY2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAY2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAY2…</td><td>&quot;\\\\tnbjm4-gpms-pi\\JMJG_U04_MAY2…</td></tr><tr><td>2022-01-01 12:00:00</td><td>&quot;3002.274902&quot;</td><td>&quot;847.7645874&quot;</td><td>&quot;593.3586426&quot;</td><td>&quot;581.3544312&quot;</td><td>&quot;12.00421143&quot;</td><td>&quot;-12.00421143&quot;</td><td>&quot;251.3350525&quot;</td><td>&quot;250.8155212&quot;</td><td>&quot;357.3814392&quot;</td><td>&quot;597.8668213&quot;</td><td>&quot;596.4998169&quot;</td><td>&quot;1.367004395&quot;</td><td>&quot;45.00209427&quot;</td><td>&quot;45.09022141&quot;</td><td>&quot;79.55097961&quot;</td><td>&quot;78.45305634&quot;</td><td>&quot;51.69094086&quot;</td><td>&quot;51.71124268&quot;</td><td>&quot;100.089386&quot;</td><td>&quot;99.99640656&quot;</td><td>&quot;96.93848419&quot;</td><td>&quot;84.41809845&quot;</td><td>&quot;537.609436&quot;</td><td>&quot;561.0895386&quot;</td><td>&quot;204.5110474&quot;</td><td>&quot;44.45517349&quot;</td><td>&quot;71.7628479&quot;</td><td>&quot;92.63828278&quot;</td><td>&quot;2.042935848&quot;</td><td>&quot;1.373728752&quot;</td><td>&quot;52.69064331&quot;</td><td>&quot;28.681427&quot;</td><td>&quot;1.020581841&quot;</td><td>&quot;1.164000034&quot;</td><td>&quot;26.07689285&quot;</td><td>&quot;26.58603477&quot;</td><td>&hellip;</td><td>&quot;78.03276062&quot;</td><td>&quot;32.11952591&quot;</td><td>&quot;1.221884966&quot;</td><td>&quot;3.449948549&quot;</td><td>&quot;83.86943817&quot;</td><td>&quot;48.28094101&quot;</td><td>&quot;2.038000107&quot;</td><td>&quot;2.747236967&quot;</td><td>&quot;75.11283875&quot;</td><td>&quot;36.79276657&quot;</td><td>&quot;0.985601187&quot;</td><td>&quot;1.35800004&quot;</td><td>&quot;20.21240234&quot;</td><td>&quot;23.7543335&quot;</td><td>&quot;0.776000023&quot;</td><td>&quot;2.114604712&quot;</td><td>&quot;3.65690136&quot;</td><td>&quot;11.03579998&quot;</td><td>&quot;13.90399933&quot;</td><td>&quot;78.41202545&quot;</td><td>&quot;706.3419189&quot;</td><td>&quot;N/A&quot;</td><td>&quot;49.72584152&quot;</td><td>&quot;3.633897066&quot;</td><td>&quot;3.609110832&quot;</td><td>&quot;0.364398897&quot;</td><td>&quot;0.265610963&quot;</td><td>&quot;251.1332703&quot;</td><td>&quot;250.5490417&quot;</td><td>&quot;24.05189514&quot;</td><td>&quot;14.95499992&quot;</td><td>&quot;-0.507251322&quot;</td><td>&quot;No Result&quot;</td><td>&quot;0.861519575&quot;</td><td>&quot;0.303608894&quot;</td><td>&quot;2.928231239&quot;</td><td>&quot;0.350361258&quot;</td></tr><tr><td>2022-01-01 12:01:00</td><td>&quot;3003.304932&quot;</td><td>&quot;847.6917725&quot;</td><td>&quot;592.4875488&quot;</td><td>&quot;580.7902222&quot;</td><td>&quot;11.69732666&quot;</td><td>&quot;-11.69732666&quot;</td><td>&quot;251.1396332&quot;</td><td>&quot;250.7506561&quot;</td><td>&quot;356.8656921&quot;</td><td>&quot;598.960083&quot;</td><td>&quot;597.3535156&quot;</td><td>&quot;1.606567383&quot;</td><td>&quot;45.00209427&quot;</td><td>&quot;45.09022141&quot;</td><td>&quot;79.55097961&quot;</td><td>&quot;78.43061829&quot;</td><td>&quot;51.90289307&quot;</td><td>&quot;51.98352432&quot;</td><td>&quot;100.089386&quot;</td><td>&quot;99.93550873&quot;</td><td>&quot;97.10176086&quot;</td><td>&quot;84.59054565&quot;</td><td>&quot;537.609436&quot;</td><td>&quot;561.0895386&quot;</td><td>&quot;204.5110474&quot;</td><td>&quot;44.43427277&quot;</td><td>&quot;79.95368958&quot;</td><td>&quot;93.85268402&quot;</td><td>&quot;2.047074318&quot;</td><td>&quot;1.401898742&quot;</td><td>&quot;50.86927795&quot;</td><td>&quot;26.55750847&quot;</td><td>&quot;0.977332175&quot;</td><td>&quot;1.164000034&quot;</td><td>&quot;25.74589539&quot;</td><td>&quot;25.6198616&quot;</td><td>&hellip;</td><td>&quot;79.85456848&quot;</td><td>&quot;32.64984894&quot;</td><td>&quot;1.211074829&quot;</td><td>&quot;3.402988434&quot;</td><td>&quot;84.04837036&quot;</td><td>&quot;48.04061127&quot;</td><td>&quot;2.038000107&quot;</td><td>&quot;2.767861605&quot;</td><td>&quot;72.96357727&quot;</td><td>&quot;36.02448654&quot;</td><td>&quot;0.971089602&quot;</td><td>&quot;1.35800004&quot;</td><td>&quot;20.50992393&quot;</td><td>&quot;24.61970711&quot;</td><td>&quot;0.776000023&quot;</td><td>&quot;2.126266718&quot;</td><td>&quot;3.657661915&quot;</td><td>&quot;11.03579998&quot;</td><td>&quot;13.90399933&quot;</td><td>&quot;78.41202545&quot;</td><td>&quot;705.8787231&quot;</td><td>&quot;N/A&quot;</td><td>&quot;49.72584152&quot;</td><td>&quot;3.626084566&quot;</td><td>&quot;3.593890905&quot;</td><td>&quot;0.363058925&quot;</td><td>&quot;0.265159667&quot;</td><td>&quot;251.3414307&quot;</td><td>&quot;250.0171356&quot;</td><td>&quot;24.05379677&quot;</td><td>&quot;14.95499992&quot;</td><td>&quot;-0.508443236&quot;</td><td>&quot;No Result&quot;</td><td>&quot;0.756399214&quot;</td><td>&quot;0.303608894&quot;</td><td>&quot;2.185158968&quot;</td><td>&quot;0.080158211&quot;</td></tr><tr><td>2022-01-01 12:02:00</td><td>&quot;3003.746338&quot;</td><td>&quot;850.3841553&quot;</td><td>&quot;592.4807739&quot;</td><td>&quot;580.8744507&quot;</td><td>&quot;11.60632324&quot;</td><td>&quot;-11.60632324&quot;</td><td>&quot;251.4239044&quot;</td><td>&quot;250.9886475&quot;</td><td>&quot;356.729126&quot;</td><td>&quot;599.9021606&quot;</td><td>&quot;598.8768921&quot;</td><td>&quot;1.025268555&quot;</td><td>&quot;45.00209427&quot;</td><td>&quot;45.09022141&quot;</td><td>&quot;79.55097961&quot;</td><td>&quot;78.40825653&quot;</td><td>&quot;51.84835434&quot;</td><td>&quot;51.74568176&quot;</td><td>&quot;100.089386&quot;</td><td>&quot;100.1313324&quot;</td><td>&quot;96.88920593&quot;</td><td>&quot;84.55322266&quot;</td><td>&quot;537.609436&quot;</td><td>&quot;561.0895386&quot;</td><td>&quot;204.5110474&quot;</td><td>&quot;44.41337204&quot;</td><td>&quot;78.74462891&quot;</td><td>&quot;93.04692078&quot;</td><td>&quot;2.0600245&quot;</td><td>&quot;1.430068731&quot;</td><td>&quot;52.82896042&quot;</td><td>&quot;27.41456985&quot;</td><td>&quot;1.059352398&quot;</td><td>&quot;1.164000034&quot;</td><td>&quot;26.06288147&quot;</td><td>&quot;25.88448715&quot;</td><td>&hellip;</td><td>&quot;77.80049133&quot;</td><td>&quot;31.73116302&quot;</td><td>&quot;1.200264692&quot;</td><td>&quot;3.468194246&quot;</td><td>&quot;84.09593964&quot;</td><td>&quot;49.17975235&quot;</td><td>&quot;2.038000107&quot;</td><td>&quot;2.791003466&quot;</td><td>&quot;74.6672821&quot;</td><td>&quot;36.84172821&quot;</td><td>&quot;1.000359893&quot;</td><td>&quot;1.35800004&quot;</td><td>&quot;21.32362175&quot;</td><td>&quot;24.27088165&quot;</td><td>&quot;0.776000023&quot;</td><td>&quot;2.048444986&quot;</td><td>&quot;3.658142805&quot;</td><td>&quot;11.03579998&quot;</td><td>&quot;13.90399933&quot;</td><td>&quot;78.41202545&quot;</td><td>&quot;705.4155884&quot;</td><td>&quot;N/A&quot;</td><td>&quot;49.72584152&quot;</td><td>&quot;3.60290432&quot;</td><td>&quot;3.587474108&quot;</td><td>&quot;0.361718953&quot;</td><td>&quot;0.26470834&quot;</td><td>&quot;251.5495911&quot;</td><td>&quot;250.3991547&quot;</td><td>&quot;24.0557003&quot;</td><td>&quot;14.95499992&quot;</td><td>&quot;-0.510388553&quot;</td><td>&quot;No Result&quot;</td><td>&quot;0.981265664&quot;</td><td>&quot;0.303608894&quot;</td><td>&quot;0.410140693&quot;</td><td>&quot;-0.190044835&quot;</td></tr><tr><td>2022-01-01 12:03:00</td><td>&quot;3003.915039&quot;</td><td>&quot;850.3084717&quot;</td><td>&quot;593.0683594&quot;</td><td>&quot;581.3079224&quot;</td><td>&quot;11.76043701&quot;</td><td>&quot;-11.76043701&quot;</td><td>&quot;251.7081909&quot;</td><td>&quot;251.2266388&quot;</td><td>&quot;356.9035645&quot;</td><td>&quot;600.4395142&quot;</td><td>&quot;599.3977051&quot;</td><td>&quot;1.041809082&quot;</td><td>&quot;45.00209427&quot;</td><td>&quot;45.09022141&quot;</td><td>&quot;79.55097961&quot;</td><td>&quot;78.65039063&quot;</td><td>&quot;51.70002747&quot;</td><td>&quot;51.56722641&quot;</td><td>&quot;100.0693588&quot;</td><td>&quot;99.95241547&quot;</td><td>&quot;97.02642059&quot;</td><td>&quot;84.51694489&quot;</td><td>&quot;537.609436&quot;</td><td>&quot;561.0895386&quot;</td><td>&quot;204.5110474&quot;</td><td>&quot;44.3924675&quot;</td><td>&quot;71.56343079&quot;</td><td>&quot;89.84835815&quot;</td><td>&quot;2.067594051&quot;</td><td>&quot;1.364515424&quot;</td><td>&quot;51.47906876&quot;</td><td>&quot;27.57266808&quot;</td><td>&quot;1.047598004&quot;</td><td>&quot;1.164000034&quot;</td><td>&quot;27.66227722&quot;</td><td>&quot;25.60324478&quot;</td><td>&hellip;</td><td>&quot;79.32099915&quot;</td><td>&quot;31.81253433&quot;</td><td>&quot;1.189454556&quot;</td><td>&quot;3.411828756&quot;</td><td>&quot;83.71022034&quot;</td><td>&quot;49.34534454&quot;</td><td>&quot;2.038000107&quot;</td><td>&quot;2.790816069&quot;</td><td>&quot;72.83457184&quot;</td><td>&quot;36.04971695&quot;</td><td>&quot;0.972605407&quot;</td><td>&quot;1.35800004&quot;</td><td>&quot;21.60121727&quot;</td><td>&quot;24.48646355&quot;</td><td>&quot;0.776000023&quot;</td><td>&quot;2.084728241&quot;</td><td>&quot;3.66174531&quot;</td><td>&quot;11.03579998&quot;</td><td>&quot;13.90399933&quot;</td><td>&quot;78.41202545&quot;</td><td>&quot;704.9523926&quot;</td><td>&quot;N/A&quot;</td><td>&quot;49.72584152&quot;</td><td>&quot;3.61432457&quot;</td><td>&quot;3.596920729&quot;</td><td>&quot;0.360378981&quot;</td><td>&quot;0.264257044&quot;</td><td>&quot;251.7577515&quot;</td><td>&quot;250.8899231&quot;</td><td>&quot;24.05760193&quot;</td><td>&quot;14.95499992&quot;</td><td>&quot;-0.51299423&quot;</td><td>&quot;No Result&quot;</td><td>&quot;1.95964098&quot;</td><td>&quot;0.303608894&quot;</td><td>&quot;1.721817255&quot;</td><td>&quot;-0.460247874&quot;</td></tr></tbody></table></div>"], "text/plain": ["shape: (5, 76)\n", "┌───────────┬───────────┬───────────┬───────────┬───┬───────────┬───────────┬───────────┬──────────┐\n", "│ Date &    ┆ Shaft     ┆ Power     ┆ Live      ┆ … ┆ HP_Rel    ┆ HP        ┆ IP_Rel    ┆ IP       │\n", "│ time      ┆ speed     ┆ output    ┆ Steam Tem ┆   ┆ Stress    ┆ Surface   ┆ Stress    ┆ Surface  │\n", "│ stamp [DD ┆ (RPM)     ┆ (MW)      ┆ perature  ┆   ┆ ---       ┆ Stress    ┆ ---       ┆ Stress   │\n", "│ :MMM:YYYY ┆ ---       ┆ ---       ┆ A (°C)    ┆   ┆ str       ┆ ---       ┆ str       ┆ ---      │\n", "│ …         ┆ str       ┆ str       ┆ ---       ┆   ┆           ┆ str       ┆           ┆ str      │\n", "│ ---       ┆           ┆           ┆ str       ┆   ┆           ┆           ┆           ┆          │\n", "│ datetime[ ┆           ┆           ┆           ┆   ┆           ┆           ┆           ┆          │\n", "│ μs]       ┆           ┆           ┆           ┆   ┆           ┆           ┆           ┆          │\n", "╞═══════════╪═══════════╪═══════════╪═══════════╪═══╪═══════════╪═══════════╪═══════════╪══════════╡\n", "│ null      ┆ \\\\tnbjm4- ┆ \\\\tnbjm4- ┆ \\\\tnbjm4- ┆ … ┆ \\\\tnbjm4- ┆ \\\\tnbjm4- ┆ \\\\tnbjm4- ┆ \\\\tnbjm4 │\n", "│           ┆ gpms-pi\\J ┆ gpms-pi\\J ┆ gpms-pi\\J ┆   ┆ gpms-pi\\J ┆ gpms-pi\\J ┆ gpms-pi\\J ┆ -gpms-pi │\n", "│           ┆ MJG_U04_M ┆ MJG_U04_B ┆ MJG_U04_L ┆   ┆ MJG_U04_M ┆ MJG_U04_M ┆ MJG_U04_M ┆ \\JMJG_U0 │\n", "│           ┆ AD2…      ┆ AA5…      ┆ BA1…      ┆   ┆ AY2…      ┆ AY2…      ┆ AY2…      ┆ 4_MAY2…  │\n", "│ 2022-01-0 ┆ 3002.2749 ┆ 847.76458 ┆ 593.35864 ┆ … ┆ 0.8615195 ┆ 0.3036088 ┆ 2.9282312 ┆ 0.350361 │\n", "│ 1         ┆ 02        ┆ 74        ┆ 26        ┆   ┆ 75        ┆ 94        ┆ 39        ┆ 258      │\n", "│ 12:00:00  ┆           ┆           ┆           ┆   ┆           ┆           ┆           ┆          │\n", "│ 2022-01-0 ┆ 3003.3049 ┆ 847.69177 ┆ 592.48754 ┆ … ┆ 0.7563992 ┆ 0.3036088 ┆ 2.1851589 ┆ 0.080158 │\n", "│ 1         ┆ 32        ┆ 25        ┆ 88        ┆   ┆ 14        ┆ 94        ┆ 68        ┆ 211      │\n", "│ 12:01:00  ┆           ┆           ┆           ┆   ┆           ┆           ┆           ┆          │\n", "│ 2022-01-0 ┆ 3003.7463 ┆ 850.38415 ┆ 592.48077 ┆ … ┆ 0.9812656 ┆ 0.3036088 ┆ 0.4101406 ┆ -0.19004 │\n", "│ 1         ┆ 38        ┆ 53        ┆ 39        ┆   ┆ 64        ┆ 94        ┆ 93        ┆ 4835     │\n", "│ 12:02:00  ┆           ┆           ┆           ┆   ┆           ┆           ┆           ┆          │\n", "│ 2022-01-0 ┆ 3003.9150 ┆ 850.30847 ┆ 593.06835 ┆ … ┆ 1.9596409 ┆ 0.3036088 ┆ 1.7218172 ┆ -0.46024 │\n", "│ 1         ┆ 39        ┆ 17        ┆ 94        ┆   ┆ 8         ┆ 94        ┆ 55        ┆ 7874     │\n", "│ 12:03:00  ┆           ┆           ┆           ┆   ┆           ┆           ┆           ┆          │\n", "└───────────┴───────────┴───────────┴───────────┴───┴───────────┴───────────┴───────────┴──────────┘"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pl.read_csv(\"../data/2022-2023.csv\", infer_schema=False, low_memory=False)\n", "            \n", "# Convert timestamp strings to pl.Datetime object, sort chronologically\n", "df = df.with_columns(\n", "            pl.col(\"Date & time stamp [DD:MMM:YYYY   HH:MM:SS]\").str.strptime( # Select timestamp column and parse string\n", "            pl.Datetime, # Convert to datetime object\n", "            format=\"%d-%b-%y %H:%M:%S\"\n", "            ))\n", "\n", "df = df.sort(\"Date & time stamp [DD:MMM:YYYY   HH:MM:SS]\")\n", "\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Rename columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def clean_column(col):\n", "    col = col.strip()  # Remove leading and trailing whitespace\n", "    col = re.sub(\n", "        r\"(?i)\\([^\\)]*°?C[^\\)]*\\)\", \"(celcius)\", col\n", "    )  # Replace any (°C), (<PERSON><PERSON><PERSON>), etc. with celcius\n", "    col = re.sub(r\"\\s+\", \"_\", col)  # Replace spaces with underscores\n", "    col = col.strip(\"_\")  # Strip leading and trailing underscores\n", "    return col"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Date & time stamp [DD:MMM:YYYY   HH:MM:SS]'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Store the column renames, including overriding manual edits!\n", "rename_dict = {col: clean_column(col) for col in df.columns}\n", "rename_dict[\"Date & time stamp [DD:MMM:YYYY   HH:MM:SS]\"] = \"datetime\"\n", "rename_dict[\"Reheat Steam Temperature B(Celcius)\"] = (\n", "    \"Reheat_Steam_Temperature_B_(celcius)\"\n", ")\n", "\n", "df = df.rename(rename_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["961925"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"../data/rename_dict.json\", \"w\") as f:\n", "    json.dump(rename_dict, f, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.to_pandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def group_times(times, max_gap=pd.Timedelta(hours=1)):\n", "    times = times.sort_values()\n", "    ranges = []\n", "    start = end = times.iloc[0]\n", "\n", "    for t in times.iloc[1:]:\n", "        if t - end <= max_gap:\n", "            end = t\n", "        else:\n", "            ranges.append((start, end))\n", "            start = end = t\n", "    ranges.append((start, end))\n", "    return ranges"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A-B mismatch:\n", "2023-02-01 12:00:00 to 2023-02-02 10:11:00\n", "2023-02-02 11:21:00 to 2023-02-03 23:58:00\n", "2023-02-07 10:51:00 to 2023-03-01 12:00:00\n", "2023-10-01 12:01:00 to 2023-10-06 01:45:00\n", "2023-10-06 03:33:00 to 2023-11-01 11:59:00\n", "2023-12-01 12:00:00 to 2023-12-01 15:51:00\n", "2023-12-01 16:58:00 to 2023-12-31 20:35:00\n", "\n", "B-A mismatch:\n", "2023-02-09 04:23:00 to 2023-03-01 12:00:00\n", "2023-12-01 12:00:00 to 2023-12-01 15:51:00\n", "2023-12-01 17:18:00 to 2023-12-31 20:35:00\n"]}], "source": ["a = df[\"Live_Steam_Temperature_A_(celcius)\"]\n", "b = df[\"Live_Steam_Temperature_B_(celcius)\"]\n", "ab = df[\"Delta_Temp_(A-B)\"]\n", "ba = df[\"Delta_Temp_(B-A)\"]\n", "timestamps = df[\"datetime\"]\n", "\n", "ab_mismatch = ((a - b - ab).abs() > 0.01) & ab.notna()\n", "ba_mismatch = ((b - a - ba).abs() > 0.01) & ba.notna()\n", "\n", "ab_times = timestamps[ab_mismatch]\n", "ba_times = timestamps[ba_mismatch]\n", "\n", "ab_ranges = group_times(ab_times)\n", "ba_ranges = group_times(ba_times)\n", "\n", "# Print mismatches\n", "print(\"A-B mismatch:\")\n", "for s, e in ab_ranges:\n", "    print(f\"{s} to {e}\")\n", "\n", "print(\"\\nB-A mismatch:\")\n", "for s, e in ba_ranges:\n", "    print(f\"{s} to {e}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Drop duplicated row with different values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>Shaft_speed_(RPM)</th>\n", "      <th>Power_output_(MW)</th>\n", "      <th>Live_Steam_Temperature_A_(celcius)</th>\n", "      <th>Live_Steam_Temperature_B_(celcius)</th>\n", "      <th>Delta_Temp_(A-B)</th>\n", "      <th>Delta_Temp_(B-A)</th>\n", "      <th>Live_Steam_Pressure_A_(bar)</th>\n", "      <th>Live_Steam_Pressure_B_(bar)</th>\n", "      <th>T_HP_Exhaust</th>\n", "      <th>...</th>\n", "      <th>P_MSV(A)_Casing</th>\n", "      <th>P_MSV(B)_Casing</th>\n", "      <th>HP_ABS_Exp</th>\n", "      <th>IP_ABS_Exp</th>\n", "      <th>Thrust_Position</th>\n", "      <th>HP_Eccentricity</th>\n", "      <th>HP_Rel_Stress</th>\n", "      <th>HP_Surface_Stress</th>\n", "      <th>IP_Rel_Stress</th>\n", "      <th>IP_Surface_Stress</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>917281</th>\n", "      <td>2023-12-01 11:58:00</td>\n", "      <td>16.013706</td>\n", "      <td>-1.177086</td>\n", "      <td>419.195251</td>\n", "      <td>464.250702</td>\n", "      <td>-45.055450</td>\n", "      <td>45.055450</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>311.073639</td>\n", "      <td>...</td>\n", "      <td>0.381867</td>\n", "      <td>0.220306</td>\n", "      <td>22.463921</td>\n", "      <td>14.860000</td>\n", "      <td>-0.090158</td>\n", "      <td>NaN</td>\n", "      <td>0.230896</td>\n", "      <td>-0.848943</td>\n", "      <td>0.117488</td>\n", "      <td>0.650814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>917282</th>\n", "      <td>2023-12-01 11:59:00</td>\n", "      <td>16.013611</td>\n", "      <td>-1.177086</td>\n", "      <td>419.309601</td>\n", "      <td>464.280395</td>\n", "      <td>-44.970795</td>\n", "      <td>44.970795</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>311.042664</td>\n", "      <td>...</td>\n", "      <td>0.381867</td>\n", "      <td>0.220306</td>\n", "      <td>22.460175</td>\n", "      <td>14.860000</td>\n", "      <td>-0.078936</td>\n", "      <td>NaN</td>\n", "      <td>0.230896</td>\n", "      <td>-0.848943</td>\n", "      <td>0.117488</td>\n", "      <td>0.650814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>917283</th>\n", "      <td>2023-12-01 12:00:00</td>\n", "      <td>16.014111</td>\n", "      <td>-1.177086</td>\n", "      <td>419.210876</td>\n", "      <td>464.149536</td>\n", "      <td>-44.938660</td>\n", "      <td>44.938660</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>310.987244</td>\n", "      <td>...</td>\n", "      <td>0.381867</td>\n", "      <td>0.220306</td>\n", "      <td>22.457743</td>\n", "      <td>14.860000</td>\n", "      <td>-0.082503</td>\n", "      <td>NaN</td>\n", "      <td>0.230896</td>\n", "      <td>-0.848943</td>\n", "      <td>0.117488</td>\n", "      <td>0.650814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>917284</th>\n", "      <td>2023-12-01 12:00:00</td>\n", "      <td>16.014111</td>\n", "      <td>-1.177086</td>\n", "      <td>419.210876</td>\n", "      <td>464.149536</td>\n", "      <td>44.938660</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>310.987244</td>\n", "      <td>415.472260</td>\n", "      <td>...</td>\n", "      <td>0.220306</td>\n", "      <td>22.457743</td>\n", "      <td>14.860000</td>\n", "      <td>-0.082503</td>\n", "      <td>NaN</td>\n", "      <td>0.230896</td>\n", "      <td>-0.848943</td>\n", "      <td>0.117488</td>\n", "      <td>0.650814</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>917285</th>\n", "      <td>2023-12-01 12:01:00</td>\n", "      <td>16.033361</td>\n", "      <td>-1.177086</td>\n", "      <td>419.195648</td>\n", "      <td>464.285767</td>\n", "      <td>45.090118</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>310.981262</td>\n", "      <td>415.332825</td>\n", "      <td>...</td>\n", "      <td>0.220306</td>\n", "      <td>22.455376</td>\n", "      <td>14.860000</td>\n", "      <td>-0.083795</td>\n", "      <td>NaN</td>\n", "      <td>0.230896</td>\n", "      <td>-0.848943</td>\n", "      <td>0.117488</td>\n", "      <td>0.650814</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>917286</th>\n", "      <td>2023-12-01 12:02:00</td>\n", "      <td>16.031923</td>\n", "      <td>-1.177086</td>\n", "      <td>419.174133</td>\n", "      <td>464.187073</td>\n", "      <td>45.012939</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>310.950409</td>\n", "      <td>415.277161</td>\n", "      <td>...</td>\n", "      <td>0.220306</td>\n", "      <td>22.453009</td>\n", "      <td>14.860000</td>\n", "      <td>-0.081345</td>\n", "      <td>NaN</td>\n", "      <td>0.230896</td>\n", "      <td>-0.848943</td>\n", "      <td>0.117488</td>\n", "      <td>0.650814</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6 rows × 76 columns</p>\n", "</div>"], "text/plain": ["                  datetime  Shaft_speed_(RPM)  Power_output_(MW)  \\\n", "917281 2023-12-01 11:58:00          16.013706          -1.177086   \n", "917282 2023-12-01 11:59:00          16.013611          -1.177086   \n", "917283 2023-12-01 12:00:00          16.014111          -1.177086   \n", "917284 2023-12-01 12:00:00          16.014111          -1.177086   \n", "917285 2023-12-01 12:01:00          16.033361          -1.177086   \n", "917286 2023-12-01 12:02:00          16.031923          -1.177086   \n", "\n", "        Live_Steam_Temperature_A_(celcius)  \\\n", "917281                          419.195251   \n", "917282                          419.309601   \n", "917283                          419.210876   \n", "917284                          419.210876   \n", "917285                          419.195648   \n", "917286                          419.174133   \n", "\n", "        Live_Steam_Temperature_B_(celcius)  Delta_Temp_(A-B)  \\\n", "917281                          464.250702        -45.055450   \n", "917282                          464.280395        -44.970795   \n", "917283                          464.149536        -44.938660   \n", "917284                          464.149536         44.938660   \n", "917285                          464.285767         45.090118   \n", "917286                          464.187073         45.012939   \n", "\n", "        Delta_Temp_(B-A)  Live_Steam_Pressure_A_(bar)  \\\n", "917281         45.055450                          0.0   \n", "917282         44.970795                          0.0   \n", "917283         44.938660                          0.0   \n", "917284          0.000000                          0.0   \n", "917285          0.000000                          0.0   \n", "917286          0.000000                          0.0   \n", "\n", "        Live_Steam_Pressure_B_(bar)  T_HP_Exhaust  ...  P_MSV(A)_Casing  \\\n", "917281                     0.000000    311.073639  ...         0.381867   \n", "917282                     0.000000    311.042664  ...         0.381867   \n", "917283                     0.000000    310.987244  ...         0.381867   \n", "917284                   310.987244    415.472260  ...         0.220306   \n", "917285                   310.981262    415.332825  ...         0.220306   \n", "917286                   310.950409    415.277161  ...         0.220306   \n", "\n", "        P_MSV(B)_Casing  HP_ABS_Exp  IP_ABS_Exp  Thrust_Position  \\\n", "917281         0.220306   22.463921   14.860000        -0.090158   \n", "917282         0.220306   22.460175   14.860000        -0.078936   \n", "917283         0.220306   22.457743   14.860000        -0.082503   \n", "917284        22.457743   14.860000   -0.082503              NaN   \n", "917285        22.455376   14.860000   -0.083795              NaN   \n", "917286        22.453009   14.860000   -0.081345              NaN   \n", "\n", "        HP_Eccentricity  HP_Rel_Stress  HP_Surface_Stress  IP_Rel_Stress  \\\n", "917281              NaN       0.230896          -0.848943       0.117488   \n", "917282              NaN       0.230896          -0.848943       0.117488   \n", "917283              NaN       0.230896          -0.848943       0.117488   \n", "917284         0.230896      -0.848943           0.117488       0.650814   \n", "917285         0.230896      -0.848943           0.117488       0.650814   \n", "917286         0.230896      -0.848943           0.117488       0.650814   \n", "\n", "        IP_Surface_Stress  \n", "917281           0.650814  \n", "917282           0.650814  \n", "917283           0.650814  \n", "917284                NaN  \n", "917285                NaN  \n", "917286                NaN  \n", "\n", "[6 rows x 76 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# The 2023-12-01 12:00:00 timestamp is duplicated!\n", "# The second instance onwards is a little timeshift that needs correction\n", "df.loc[917281:917286]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Visual inspection revealed that the months of Feb 2023 itself (02-01 00:00 to 03-01 12:00) and everything after the start of Dec 2023 (12-01 12:00) onwards are misaligned. The Delta_Temp_(A-B) column is eaten up and data from all columns are shifted to the left. Looks like human error which we can fix."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feb_start_time = pd.to_datetime(\"2023-02-01 00:00:00\")\n", "feb_end_time = pd.to_datetime(\"2023-03-01 12:00:00\")\n", "\n", "dec_start_time = pd.to_datetime(\"2023-12-01 12:00:00\")\n", "\n", "feb_mask = (df[\"datetime\"] >= feb_start_time) & (df[\"datetime\"] <= feb_end_time)\n", "dec_mask = df[\"datetime\"] > dec_start_time\n", "\n", "# The pipe symbol (|) is an OR operator\n", "combined_mask = feb_mask | dec_mask\n", "\n", "start_idx = df.columns.get_loc(\"Delta_Temp_(B-A)\")\n", "misaligned_cols = df.columns[start_idx:]\n", "\n", "for i in range(len(misaligned_cols) - 1, 0, -1):\n", "    df.loc[combined_mask, misaligned_cols[i]] = df.loc[\n", "        combined_mask, misaligned_cols[i - 1]\n", "    ]\n", "\n", "# Set `Delta_Temp_(B-A)` to null\n", "df.loc[combined_mask, misaligned_cols[0]] = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Fix column misalignment: `02-01 00:00` to `03-01 12:00` and `12-01 12:00` onwards"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Recalculate `Delta_Temp_(A-B)` and `Delta_Temp_(B-A)`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Delta_Temp_(A-B)\"] = (\n", "    df[\"Live_Steam_Temperature_A_(celcius)\"] - df[\"Live_Steam_Temperature_B_(celcius)\"]\n", ")\n", "df[\"Delta_Temp_(B-A)\"] = (\n", "    df[\"Live_Steam_Temperature_B_(celcius)\"] - df[\"Live_Steam_Temperature_A_(celcius)\"]\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# What was this for?\n", "start_time = pd.to_datetime('2023-12-01 00:00:00')\n", "end_time = pd.to_datetime('2023-12-31 00:00:00')\n", "\n", "window_data = df[(df['datetime'] >= start_time) & (df['datetime'] <= end_time)]\n", "\n", "window_data.to_csv(\"Dec-new-2023.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.to_pandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A-B mismatch:\n", "2023-02-01 12:00:00 to 2023-02-02 10:11:00\n", "2023-02-02 11:21:00 to 2023-02-03 23:58:00\n", "2023-02-07 10:51:00 to 2023-03-01 12:00:00\n", "2023-10-01 12:01:00 to 2023-10-06 01:45:00\n", "2023-10-06 03:33:00 to 2023-11-01 11:59:00\n", "2023-12-01 12:00:00 to 2023-12-01 15:51:00\n", "2023-12-01 16:58:00 to 2023-12-31 20:35:00\n", "\n", "B-A mismatch:\n", "2023-02-09 04:23:00 to 2023-03-01 12:00:00\n", "2023-12-01 12:00:00 to 2023-12-01 15:51:00\n", "2023-12-01 17:18:00 to 2023-12-31 20:35:00\n"]}], "source": ["def group_times(times, max_gap=pd.Timedelta(hours=1)):\n", "    times = times.sort_values()\n", "    ranges = []\n", "    start = end = times.iloc[0]\n", "\n", "    for t in times.iloc[1:]:\n", "        if t - end <= max_gap:\n", "            end = t\n", "        else:\n", "            ranges.append((start, end))\n", "            start = end = t\n", "    ranges.append((start, end))\n", "    return ranges\n", "\n", "a = df[\"Live_Steam_Temperature_A_(celcius)\"]\n", "b = df[\"Live_Steam_Temperature_B_(celcius)\"]\n", "ab = df[\"Delta_Temp_(A-B)\"]\n", "ba = df[\"Delta_Temp_(B-A)\"]\n", "timestamps = df[\"Datetime\"]\n", "\n", "ab_mismatch = ((a - b - ab).abs() > 0.01) & ab.notna()\n", "ba_mismatch = ((b - a - ba).abs() > 0.01) & ba.notna()\n", "\n", "ab_times = timestamps[ab_mismatch]\n", "ba_times = timestamps[ba_mismatch]\n", "\n", "ab_ranges = group_times(ab_times)\n", "ba_ranges = group_times(ba_times)\n", "\n", "# Print mismatches\n", "print(\"A-B mismatch:\")\n", "for s, e in ab_ranges:\n", "    print(f\"{s} to {e}\")\n", "\n", "print(\"\\nB-A mismatch:\")\n", "for s, e in ba_ranges:\n", "    print(f\"{s} to {e}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Drop duplicated row with different values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['index'] = df.reset_index().index\n", "\n", "df = df[df['index'] != 917284].drop(columns=['index'])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Fix column misalignment: `02-01 00:00` to `03-01 12:00` and `12-01 12:00` onwards"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feb_start_time = pd.to_datetime(\"2023-02-01 00:00:00\")\n", "feb_end_time = pd.to_datetime(\"2023-03-01 12:00:00\")\n", "\n", "dec_start_time = pd.to_datetime(\"2023-12-01 12:00:00\")\n", "\n", "feb_mask = (df[\"Datetime\"] >= feb_start_time) & (df[\"Datetime\"] <= feb_end_time)\n", "dec_mask = df[\"Datetime\"] > dec_start_time\n", "\n", "combined_mask = feb_mask | dec_mask\n", "\n", "start_idx = df.columns.get_loc(\"Delta_Temp_(B-A)\")\n", "misaligned_cols = df.columns[start_idx:]\n", "\n", "for i in range(len(misaligned_cols) - 1, 0, -1):\n", "    df.loc[combined_mask, misaligned_cols[i]] = df.loc[combined_mask, misaligned_cols[i-1]]\n", "\n", "# Set `Delta_Temp_(B-A)` to null\n", "df.loc[combined_mask, misaligned_cols[0]] = None\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Recalculate `Delta_Temp_(A-B)` and `Delta_Temp_(B-A)`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"Delta_Temp_(A-B)\"] = df[\"Live_Steam_Temperature_A_(celcius)\"] - df[\"Live_Steam_Temperature_B_(celcius)\"]\n", "df[\"Delta_Temp_(B-A)\"] = df[\"Live_Steam_Temperature_B_(celcius)\"] - df[\"Live_Steam_Temperature_A_(celcius)\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time = pd.to_datetime('2023-12-01 00:00:00')\n", "end_time = pd.to_datetime('2023-12-31 00:00:00')\n", "\n", "window_data = df[(df['Datetime'] >= start_time) & (df['Datetime'] <= end_time)]\n", "\n", "window_data.to_csv(\"Dec-new-2023.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.to_parquet(\"../data/2022-2023.parquet\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Split data by year and month, save into separate parquet file in data subfolder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pl.read_parquet(\"../data/2022-2023.parquet\")\n", "\n", "output_dir = \"../data/data_by_month\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Extract year-month as new column\n", "df = df.with_columns([\n", "    pl.col(\"datetime\").dt.strftime(\"%Y-%m\").alias(\"year_month\")\n", "])\n", "\n", "# Group by year_month and save\n", "for ym in df[\"year_month\"].unique().to_list():\n", "    monthly_df = df.filter(pl.col(\"year_month\") == ym).drop(\"year_month\") # Drop `year_month` column when saving \n", "    monthly_df.write_parquet(f\"{output_dir}/{ym}.parquet\")\n", "\n", "df = df.drop(\"year_month\")"]}], "metadata": {"kernelspec": {"display_name": "turbine", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}, "nbdime-conflicts": {"local_diff": [{"diff": [{"diff": [{"key": 0, "op": "addrange", "valuelist": ["genco"]}, {"key": 0, "length": 1, "op": "removerange"}], "key": "display_name", "op": "patch"}, {"diff": [{"key": 0, "op": "addrange", "valuelist": ["genco"]}, {"key": 0, "length": 1, "op": "removerange"}], "key": "name", "op": "patch"}], "key": "kernelspec", "op": "patch"}], "remote_diff": [{"diff": [{"diff": [{"key": 0, "op": "addrange", "valuelist": [".venv"]}, {"key": 0, "length": 1, "op": "removerange"}], "key": "display_name", "op": "patch"}, {"diff": [{"key": 0, "op": "addrange", "valuelist": ["python3"]}, {"key": 0, "length": 1, "op": "removerange"}], "key": "name", "op": "patch"}], "key": "kernelspec", "op": "patch"}]}}, "nbformat": 4, "nbformat_minor": 4}