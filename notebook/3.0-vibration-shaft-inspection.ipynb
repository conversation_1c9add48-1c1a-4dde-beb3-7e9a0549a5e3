{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This notebook plots shaft speed of each identified trip event against time for all vibration tags, focusing on the ramp up periods. Each tag is also plotted against the known critical speed bands to see if we can correctly observe inflections of vibrations when the turbine is rotating at one of the critical speeds. Ultimately we couldn't see much as the minutely nature of data left little data points during startup to be displayed."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import polars as pl\n", "from datetime import timedelta\n", "import matplotlib.dates as mdates\n", "from matplotlib.dates import DateFormatter, HourLocator\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "%run functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_parquet(\"../data/2022-2023.parquet\")\n", "events_df = pd.read_parquet(\"../data/labels.parquet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display_scrollable_table(events_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["startup_events = events_df[events_df[\"status_change\"] == \"startup\"].copy()\n", "\n", "rows = pd.DataFrame([\n", "    {\n", "        \"event_id\": None,\n", "        \"no\": None,\n", "        \"datetime\": pd.to_datetime(\"2023-09-15 11:00\"),\n", "        \"Vibration event\": None,\n", "        \"status_change\": \"shutdown\",  \n", "        \"summary\":\"\",\n", "        \"description\": \"\"\n", "    },\n", "    {\n", "        \"event_id\": None,\n", "        \"no\": None,\n", "        \"datetime\": pd.to_datetime(\"2023-11-03 00:00\"),\n", "        \"Vibration event\": None,\n", "        \"status_change\": \"startup\", \n", "        \"summary\": \"\",\n", "        \"description\": \"\"\n", "    }\n", "])\n", "\n", "startup_events = pd.concat([startup_events, rows], ignore_index=True)\n", "\n", "startup_events.sort_values(\"datetime\", inplace=True)\n", "\n", "startup_events.reset_index(drop=True, inplace=True)\n", "startup_events[\"event_id\"] = range(1, len(startup_events) + 1)\n", "\n", "display_scrollable_table(startup_events)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Shaft speed against time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_after = pd.Timedelta(hours=24)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_figures = []\n", "max_shaft_speed = {}\n", "\n", "for _, row in startup_events.iterrows():\n", "    event_id = row[\"event_id\"]\n", "    event_time = row[\"datetime\"]\n", "\n", "    window = df[\n", "        (df[\"datetime\"] >= event_time) &\n", "        (df[\"datetime\"] <= event_time + time_after)\n", "    ]\n", "\n", "    if window[\"Shaft_speed_(RPM)\"].dropna().empty:\n", "        continue\n", "\n", "    peak_time = window.loc[window[\"Shaft_speed_(RPM)\"].idxmax(), \"datetime\"]\n", "    max_shaft_speed[event_id] = peak_time\n", "\n", "    event_speed = window.loc[window[\"datetime\"] == event_time, \"Shaft_speed_(RPM)\"].values[0]\n", "    peak_speed = window.loc[window[\"datetime\"] == peak_time, \"Shaft_speed_(RPM)\"].values[0]\n", "\n", "    plt.figure(figsize=(12, 3))\n", "    plt.plot(window[\"datetime\"], window[\"Shaft_speed_(RPM)\"], label=\"Shaft Speed (RPM)\")\n", "    plt.title(f\"Event {event_id} - Shaft Speed vs Time ({row[\"status_change\"]}) at {event_time})\")\n", "    plt.xlabel(\"Time\")\n", "    plt.ylabel(\"Shaft Speed (RPM)\")\n", "    plt.grid(True)\n", "    plt.ylim(0, 4000)\n", "\n", "    plt.axvline(event_time, color=\"green\", linestyle=\"--\", linewidth=1)\n", "\n", "    # Format ticks\n", "    xticks = list(plt.xticks()[0])\n", "    xticks.extend([mdates.date2num(event_time), mdates.date2num(peak_time)])\n", "    xticks = sorted(set(xticks))\n", "    plt.xticks(xticks, rotation=45)\n", "\n", "    ax = plt.gca()\n", "    ax.xaxis.set_major_formatter(DateFormatter(\"%m-%d %H:%M\"))\n", "    plt.gcf().autofmt_xdate()\n", "    plt.tight_layout()\n", "\n", "    fig = plt.gcf()\n", "    plot_figures.append(fig)\n", "\n", "display_scrollable_figures(plot_figures)\n", "\n", "# Print summary\n", "print(\"Time to reach peak shaft speed for each event:\")\n", "for event_id, pt in max_shaft_speed.items():\n", "    peak_speed = df.loc[\n", "        df[\"datetime\"] == pt, \"Shaft_speed_(RPM)\"\n", "    ].values[0]\n", "    print(f\"  Event {event_id}: Peak at {pt}, Shaft Speed = {peak_speed} RPM\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Vibration against time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vib_figures = []\n", "vib_pairs = [(f\"{i}X_Vib_Shaft\", f\"{i}Y_Vib_Shaft\") for i in range(1, 8)]\n", "\n", "for _, row in startup_events.iterrows():\n", "    event_id = row[\"event_id\"]\n", "    event_time = row[\"datetime\"]\n", "\n", "    window = df[\n", "        (df[\"datetime\"] >= event_time) &\n", "        (df[\"datetime\"] <= event_time + time_after)\n", "    ]\n", "\n", "    fig, axs = plt.subplots(len(vib_pairs), 1, figsize=(14, 3 * len(vib_pairs)), sharex=True)\n", "    fig.suptitle(f\"Event {event_id} - Vibration vs Time ({row[\"status_change\"]} at {event_time})\", fontsize=15)\n", "\n", "    for ax, (x_col, y_col) in zip(axs, vib_pairs):\n", "        # Vibration (left y-axis)\n", "        ax.plot(window[\"datetime\"], window[x_col], label=f\"{x_col}\", alpha=0.7, color = \"blue\")\n", "        ax.plot(window[\"datetime\"], window[y_col], label=f\"{y_col}\", alpha=0.7, color = \"orange\")\n", "        ax.set_ylabel(\"Vibration\")\n", "        \n", "        # Shaft speed (right y-axis)\n", "        ax2 = ax.twinx()\n", "        ax2.plot(window[\"datetime\"], window[\"Shaft_speed_(RPM)\"], \n", "                 color=\"gray\", linestyle=\"-\", alpha=0.3, label=\"Shaft Speed\")\n", "        ax2.set_ylabel(\"Shaft Speed (RPM)\", color=\"gray\")\n", "        ax2.tick_params(axis=\"y\", labelcolor=\"gray\")\n", "\n", "        ax.set_ylim(0, 400)\n", "        ax2.set_ylim(0, 4000)\n", "\n", "\n", "        # Event/peak time vertical lines\n", "        ax.axvline(event_time, color=\"green\", linestyle=\"--\", linewidth=1)\n", "\n", "        # Combine legends\n", "        lines, labels = ax.get_legend_handles_labels()\n", "        lines2, labels2 = ax2.get_legend_handles_labels()\n", "        ax.legend(lines + lines2, labels + labels2, loc=\"upper right\")\n", "\n", "    axs[-1].set_xlabel(\"Time\")\n", "    axs[-1].xaxis.set_major_formatter(DateFormatter(\"%m-%d %H:%M\"))\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout(rect=[0, 0.03, 1, 0.97])\n", "\n", "    vib_figures.append(fig)\n", "\n", "# Display scrollable combined figures\n", "display_scrollable_figures(vib_figures)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Extract ramp-up periods from shaft speed data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_ramp_up_periods(\n", "        df, \n", "        rpm_col=\"shaft_speed\",\n", "        time_col=\"timestamp\",\n", "        low_rpm_threshold=25,\n", "        stable_rpm_threshold=2950, \n", "        gradient_threshold=5,\n", "        plateau_window=5,\n", "        ramp_trigger_window=7,\n", "        ):\n", "    df = df.copy()\n", "    # Calculate gradient - difference in RPM between consecutive data\n", "    df[\"gradient\"] = df[rpm_col].diff()\n", "\n", "    ramp_segments = []\n", "    in_ramp = False # Keep track of ramp-up phase\n", "    start_idx = None # Index where current ramp-up begins\n", "\n", "    for i in range(plateau_window, len(df) - plateau_window):\n", "        recent_grads = df.loc[i - ramp_trigger_window + 1: i, \"gradient\"] # Gradients just before the current index, to detect start of ramp-up\n", "        next_rpms = df.loc[i: i + plateau_window - 1, rpm_col] # shaft speed ahead of current index, to detect end of ramp-up\n", "        next_grads = df.loc[i: i + plateau_window - 1, \"gradient\"] # Gradients ahead of current index, to check if gradients fall below threshold (plateau)\n", "\n", "        # Detect start of RPM ramp up - sharp increase in gradient over short window\n", "        if not in_ramp:\n", "            if (recent_grads > gradient_threshold).all():\n", "                start_idx = i - ramp_trigger_window + 1  # backtrack to start of gradient increase\n", "                in_ramp = True # Set as start of ramp-up\n", "        else:\n", "            if in_ramp:\n", "                # Early termination if RPM drops significantly before plateau\n", "                if df.loc[i, rpm_col] < low_rpm_threshold:\n", "                    # Backtrack to last increasing or high RPM point\n", "                    backtrack_idx = i\n", "                    while backtrack_idx > start_idx and df.loc[backtrack_idx, rpm_col] < df.loc[backtrack_idx - 1, rpm_col]:\n", "                        backtrack_idx -= 1\n", "\n", "                    end_idx = backtrack_idx\n", "                    ramp_segments.append((\n", "                        df.loc[start_idx, time_col],\n", "                        df.loc[end_idx, time_col]\n", "                    ))\n", "                    in_ramp = False\n", "                    start_idx = None\n", "                    continue\n", "\n", "            # Detect end - stable high RMP + near zero gradient\n", "            if (next_rpms > stable_rpm_threshold).all() and (next_grads.abs() < gradient_threshold).all():\n", "                end_idx = i + plateau_window - 1\n", "                ramp_segments.append((\n", "                    df.loc[start_idx, time_col],\n", "                    df.loc[end_idx, time_col]\n", "                ))\n", "                in_ramp = False\n", "                start_idx = None\n", "\n", "    return ramp_segments"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shaft_speed_figures = []\n", "\n", "# Extract ramp-up periods for each event\n", "ramp_segments_by_event = {}\n", "\n", "for _, row in startup_events.iterrows():\n", "    event_id = row[\"event_id\"]\n", "    event_time = row[\"datetime\"]\n", "\n", "    window = df[\n", "        (df[\"datetime\"] >= event_time) &\n", "        (df[\"datetime\"] <= event_time + time_after)\n", "    ]\n", "    \n", "    temp_df = window.copy()\n", "    temp_df = temp_df.rename(columns={\"datetime\": \"timestamp\", \"Shaft_speed_(RPM)\": \"shaft_speed\"})\n", "    temp_df.reset_index(drop=True, inplace=True)\n", "    \n", "    segments = extract_ramp_up_periods(\n", "        temp_df, \n", "        rpm_col=\"shaft_speed\", \n", "        time_col=\"timestamp\", \n", "        low_rpm_threshold=100, \n", "        stable_rpm_threshold=2950, \n", "        gradient_threshold=5, \n", "        plateau_window=5,\n", "        ramp_trigger_window=7\n", "    )\n", "    \n", "    ramp_segments_by_event[event_id] = segments\n", "    \n", "    fig, ax = plt.subplots(figsize=(15, 6))\n", "    ax.plot(temp_df[\"timestamp\"], temp_df[\"shaft_speed\"], label=\"Shaft Speed\", color=\"blue\")\n", "\n", "    for start, end in segments:\n", "        ax.axvspan(start, end, color=\"orange\", alpha=0.3)\n", "        # Add start and end labels, stagger x to prevent overlapping\n", "        ax.text(start - timed<PERSON>ta(minutes = 12), temp_df[\"shaft_speed\"].max() * 0.5, start.strftime(\"%H:%M:%S\"), rotation=90, color=\"green\", fontsize=10)\n", "        ax.text(end + <PERSON><PERSON><PERSON>(minutes = 2), temp_df[\"shaft_speed\"].max() * 0.5, end.strftime(\"%H:%M:%S\"), rotation=90, color=\"red\", fontsize=10)\n", "\n", "    ax.set_xlabel(\"Time\")\n", "    ax.set_ylabel(\"Shaft Speed (RPM)\")\n", "    ax.set_title(f\"Event {event_id} - Shaft Speed with Detected Ramp-Up Periods\")\n", "    ax.legend([\"Shaft Speed\", \"Ramp-Up Period\"], loc=\"upper left\")\n", "    ax.set_ylim(0, 4000)\n", "    plt.tight_layout()\n", "\n", "    shaft_speed_figures.append(fig)\n", "\n", "display_scrollable_figures(shaft_speed_figures)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for event_id, segments in ramp_segments_by_event.items():\n", "    print(f\"\\nRamp-up periods for Event {event_id}:\")\n", "    \n", "    for i, (start, end) in enumerate(segments):\n", "        duration = end - start\n", "        print(f\"  Period {i+1}: {start} to {end} (Duration: {duration})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Shaft vibration vs Shaft speed (group by event)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vib_shaft_figures = []\n", "\n", "# Iterate over startup events\n", "for _, row in startup_events.iterrows():\n", "    event_id = row[\"event_id\"]\n", "    event_time = row[\"datetime\"]\n", "    \n", "    event_segments = ramp_segments_by_event.get(event_id, [])\n", "    if not event_segments:\n", "        continue\n", "\n", "    fig, axs = plt.subplots(len(vib_pairs), 1, figsize=(6, 4 * len(vib_pairs)), sharex=True)\n", "    fig.suptitle(f\"Event {row['event_id']} - Vibration vs Shaft Speed ({row[\"status_change\"]} at {event_time})\", fontsize=10)\n", "\n", "    for ax, (x_col, y_col) in zip(axs, vib_pairs):\n", "        for i, (start, end) in enumerate(event_segments):\n", "            window = df[\n", "                (df[\"datetime\"] >= start) &\n", "                (df[\"datetime\"] <= end)\n", "            ]\n", "\n", "            shaft_speed = window[\"Shaft_speed_(RPM)\"]\n", "\n", "            ax.scatter(shaft_speed, window[x_col], label=f\"{x_col}\" if i == 0 else None, alpha=0.7, s=10, color=\"blue\")\n", "            ax.scatter(shaft_speed, window[y_col], label=f\"{y_col}\" if i == 0 else None, alpha=0.7, s=10, color=\"orange\")\n", "            \n", "        # Plot shaft speed range\n", "        ax.axvspan(480, 680, color=\"lightblue\", alpha=0.1, label=\"480-680 RPM\")\n", "        ax.axvspan(1395, 1650, color=\"skyblue\", alpha=0.1, label=\"1395-1650 RPM\")\n", "        ax.axvspan(1640, 1840, color=\"steelblue\", alpha=0.1, label=\"1640-1840 RPM\")\n", "        ax.axvspan(2080, 2280, color=\"grey\", alpha=0.1, label=\"2080-2280 RPM\")\n", "        \n", "        ax.set_ylabel(\"Vibration\")\n", "        ax.legend(loc=\"upper right\")\n", "\n", "        ax.set_xlim(0, 3500)\n", "        ax.set_ylim(0, 400)\n", "\n", "    axs[-1].set_xlabel(\"Shaft Speed (RPM)\")\n", "    plt.tight_layout(rect=[0,0.03,1,0.98])\n", "    \n", "    vib_shaft_figures.append(fig)\n", "\n", "display_scrollable_figures(vib_shaft_figures)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Shaft vibration vs Shaft speed (group by vibration pair)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vib_shaft_figures = []\n", "\n", "for x_col, y_col in vib_pairs:\n", "    fig, axs = plt.subplots(len(startup_events), 1, figsize=(6, 4 * len(startup_events)), sharex=True)\n", "    fig.suptitle(f\"{x_col} & {y_col} Vibration vs Shaft Speed Across Events\", fontsize=10)\n", "\n", "    for ax, (_, row) in zip(axs, startup_events.iterrows()):\n", "        event_id = row[\"event_id\"]\n", "        event_time = row[\"datetime\"]\n", "\n", "        event_segments = ramp_segments_by_event.get(event_id, [])\n", "        if not event_segments:\n", "            continue\n", "        \n", "        for i, (start, end) in enumerate(event_segments):\n", "            # Filter data for just this ramp segment\n", "            window = df[\n", "                (df[\"datetime\"] >= start) &\n", "                (df[\"datetime\"] <= end)\n", "            ]\n", "            if window.empty:\n", "                continue\n", "\n", "            shaft_speed = window[\"Shaft_speed_(RPM)\"]\n", "\n", "            ax.scatter(shaft_speed, window[x_col], label=f\"{x_col}\" if i == 0 else None, alpha=0.7, s=10, color=\"blue\")\n", "            ax.scatter(shaft_speed, window[y_col], label=f\"{y_col}\" if i == 0 else None, alpha=0.7, s=10, color=\"orange\")\n", "\n", "        ax.axvspan(480, 680, color=\"lightblue\", alpha=0.1, label=\"480-680 RPM\")\n", "        ax.axvspan(1395, 1650, color=\"skyblue\", alpha=0.1, label=\"1395-1650 RPM\")\n", "        ax.axvspan(1640, 1840, color=\"steelblue\", alpha=0.1, label=\"1640-1840 RPM\")\n", "        ax.axvspan(2080, 2280, color=\"grey\", alpha=0.1, label=\"2080-2280 RPM\")\n", "\n", "        ax.set_ylabel(\"Vibration\")\n", "        ax.set_title(f\"Event {event_id} - {row[\"status_change\"]} at {event_time.strftime(\"%Y-%m-%d %H:%M:%S\")}\", fontsize=8)\n", "        ax.set_xlim(0, 3500)\n", "        ax.set_ylim(0, 400)\n", "        ax.legend(loc=\"upper right\")\n", "\n", "    axs[-1].set_xlabel(\"Shaft Speed (RPM)\")\n", "    plt.tight_layout(rect=[0, 0.03, 1, 0.98])\n", "    vib_shaft_figures.append(fig)\n", "\n", "display_scrollable_figures(vib_shaft_figures)\n"]}], "metadata": {"kernelspec": {"display_name": "Python (uv)", "language": "python", "name": "uv-env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}