import io
import base64
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import pandas as pd
from IPython.display import HTML, display

# Display scrollable tables
def display_scrollable_table(df, max_height="400px"):
    """ Display a DataFrame in a scrollable HTML table."""
    # Convert df to HTML and wrap it in a scrollable container
    df = df if isinstance(df, pd.DataFrame) else df.to_pandas()
    table_html = df.to_html(classes="table table-striped", index = False)
    html_content = f'<div style="overflow:auto; max-height:{max_height}; border:1px solid #ccc;">{table_html}</div>'
    
    return HTML(html_content)

# Display scrollable figures
def display_scrollable_figures(figures, max_height="600px"):
    """Display a list of matplotlib figures in a scrollable container in Jupyter."""
    img_tags = []
    for fig in figures:
        buf = io.BytesIO()
        fig.savefig(buf, format='png', bbox_inches='tight')
        plt.close(fig)
        buf.seek(0)
        img_base64 = base64.b64encode(buf.read()).decode('utf-8')
        img_tag = f'<img src="data:image/png;base64,{img_base64}" style="max-width:100%; margin-bottom:20px;" />'
        img_tags.append(img_tag)

    html = f'<div style="overflow:auto; max-height:{max_height}; border:1px solid #ccc; padding:10px;">{"".join(img_tags)}</div>'
    display(HTML(html))

# Save figures
def save_figures_to_pdf(figures, filename="output.pdf"):
    """
    Save a list of matplotlib figures to a single multi-page PDF.
    """
    with PdfPages(filename) as pdf:
        for fig in figures:
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)

    print(f"Saved {len(figures)} figures to '{filename}'")


# Skip cells
from IPython.core.magic import register_cell_magic

@register_cell_magic
def skip(line, cell):
    return