repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: "v5.0.0"
    hooks:
      - id: check-ast
      - id: check-json
      - id: check-yaml
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  # -   repo: https://github.com/psf/black
  #     rev: "24.4.0"
  #     hooks:
  #     -   id: black

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.9.9
    hooks:
      # Equivalent to isort
      - id: ruff
        args: ["--select", "I", "--fix"]
      # Formatter, replaces black
      - id: ruff-format
        types_or: [python, pyi, jupyter]

  - repo: https://github.com/jorisroovers/gitlint
    rev: "v0.19.1"
    hooks:
      - id: gitlint
        args: [--msg-filename]
